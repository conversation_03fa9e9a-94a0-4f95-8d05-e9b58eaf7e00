Attack Path Analysis User Guide
=================================

This comprehensive guide covers how to use the attack path analysis features of the Blast-Radius Security Tool for threat modeling, risk assessment, and incident response.

Overview
--------

The attack path analysis engine provides:

* **🕸️ Attack Path Discovery**: Find multi-hop attack paths through your infrastructure
* **💥 Blast Radius Calculation**: Assess impact from compromised assets
* **🎭 Attack Scenario Modeling**: Model complex threat actor campaigns
* **🛡️ MITRE ATT&CK Integration**: Framework-based threat analysis
* **📊 Risk Assessment**: Comprehensive risk scoring and prioritization

Getting Started
---------------

Prerequisites
~~~~~~~~~~~~~

Before using attack path analysis, ensure you have:

1. **Asset Discovery Completed**: Your infrastructure assets are discovered and mapped
2. **Relationships Defined**: Asset relationships and dependencies are established
3. **Proper Permissions**: Access to attack path analysis features
4. **Graph Data Populated**: The graph engine has current asset and relationship data

Quick Start Example
~~~~~~~~~~~~~~~~~~~

Here's a simple example to get you started:

1. **Navigate to Attack Path Analysis**
   
   Go to the Attack Path Analysis section in the web interface or use the API directly.

2. **Select Source Asset**
   
   Choose a potential entry point (e.g., public-facing web server, user workstation).

3. **Define Targets**
   
   Select high-value targets (e.g., databases, critical servers, sensitive data stores).

4. **Run Analysis**
   
   Execute the attack path analysis to discover potential attack routes.

5. **Review Results**
   
   Examine discovered paths, risk scores, and recommended mitigations.

Core Concepts
-------------

Attack Path Types
~~~~~~~~~~~~~~~~~

The system identifies six types of attack paths:

**1. Direct Attacks**
   Simple point-to-point attacks with minimal hops.
   
   *Example*: Internet → Web Server → Database

**2. Lateral Movement**
   Multi-hop attacks across network segments.
   
   *Example*: Workstation → File Server → Domain Controller → Database

**3. Privilege Escalation**
   Attacks that gain progressively higher privileges.
   
   *Example*: User Account → Service Account → Admin Account → Critical System

**4. Data Exfiltration**
   Attacks specifically targeting data assets.
   
   *Example*: Compromised Endpoint → Network Share → Backup Server → Cloud Storage

**5. Supply Chain**
   Attacks through third-party dependencies.
   
   *Example*: Vendor System → Management Interface → Production Environment

**6. Insider Threat**
   Attacks from internal actors with existing access.
   
   *Example*: Employee Workstation → Admin Tools → Critical Infrastructure

Risk Scoring Methodology
~~~~~~~~~~~~~~~~~~~~~~~~~

Attack paths are scored using multiple factors:

**Risk Score (0-100)**
   * Asset risk scores (weighted average)
   * Path complexity (length and difficulty)
   * Security controls (encryption, authentication, monitoring)
   * Business criticality of target assets

**Likelihood (0-1)**
   * Base probability of success
   * Security control effectiveness
   * Monitoring and detection capabilities
   * Authentication requirements

**Impact Score (0-100)**
   * Business criticality of target
   * Data classification level
   * PII and compliance considerations
   * Financial and operational impact

**Criticality Levels**
   * **CRITICAL** (80-100): Immediate attention required
   * **HIGH** (60-79): High priority for remediation
   * **MEDIUM** (40-59): Moderate risk, plan remediation
   * **LOW** (0-39): Lower priority, monitor

Using the Web Interface
-----------------------

Attack Path Discovery
~~~~~~~~~~~~~~~~~~~~~

1. **Access the Interface**
   
   Navigate to **Security Analysis** → **Attack Paths** in the main menu.

2. **Configure Analysis**
   
   * **Source Asset**: Select or search for the starting point
   * **Target Assets**: Choose specific targets or use "All High-Value Targets"
   * **Max Path Length**: Set maximum number of hops (default: 5)
   * **Max Paths**: Limit number of results per target (default: 5)

3. **Execute Analysis**
   
   Click **"Analyze Attack Paths"** to start the analysis.

4. **Review Results**
   
   Results are displayed with:
   * Visual path representation
   * Risk scores and criticality levels
   * MITRE ATT&CK technique mappings
   * Estimated attack time and resources
   * Recommended mitigations

Blast Radius Analysis
~~~~~~~~~~~~~~~~~~~~~

1. **Select Compromised Asset**
   
   Choose an asset to simulate compromise.

2. **Configure Parameters**
   
   * **Max Degrees**: Set propagation depth (default: 5)
   * **Impact Factors**: Choose what to include in impact calculation

3. **Run Calculation**
   
   Execute blast radius analysis.

4. **Analyze Results**
   
   Review:
   * Total affected assets
   * Impact by degree of separation
   * Critical and data assets affected
   * Financial impact estimation
   * Recovery time estimates
   * Compliance implications

Attack Scenario Modeling
~~~~~~~~~~~~~~~~~~~~~~~~

1. **Create New Scenario**
   
   Click **"New Attack Scenario"** and provide:
   * Scenario name and description
   * Threat actor or group
   * Entry points (multiple allowed)
   * Objectives (target assets)

2. **Configure Threat Actor**
   
   Select or define:
   * Capability level
   * Available resources
   * Typical techniques used
   * Persistence requirements

3. **Generate Scenario**
   
   The system will:
   * Find all possible attack paths
   * Calculate scenario risk and likelihood
   * Estimate required resources and time
   * Generate MITRE ATT&CK mappings
   * Suggest detection and mitigation strategies

4. **Review and Export**
   
   * Examine complete scenario analysis
   * Export reports for stakeholders
   * Save scenarios for future reference

Using the API
-------------

Basic Attack Path Analysis
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    import requests
    
    # Configure API client
    api_base = "https://your-blast-radius-instance.com/api/v1"
    headers = {"Authorization": "Bearer your-jwt-token"}
    
    # Analyze attack paths
    response = requests.post(
        f"{api_base}/attack-paths/analyze",
        headers=headers,
        json={
            "source_asset_id": "web_server_001",
            "target_asset_ids": ["database_001", "backup_server_001"],
            "max_path_length": 5,
            "max_paths_per_target": 3
        }
    )
    
    paths = response.json()
    
    # Process results
    for path in paths:
        print(f"Path: {' → '.join(path['path_nodes'])}")
        print(f"Risk Score: {path['risk_score']:.1f}")
        print(f"Criticality: {path['criticality_level']}")
        print(f"Techniques: {', '.join(path['attack_techniques'])}")
        print("---")

Blast Radius Calculation
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    # Calculate blast radius
    response = requests.post(
        f"{api_base}/attack-paths/blast-radius",
        headers=headers,
        json={
            "source_asset_id": "compromised_server",
            "max_degrees": 3
        }
    )
    
    blast_result = response.json()
    
    print(f"Affected Assets: {len(blast_result['affected_assets'])}")
    print(f"Critical Assets: {len(blast_result['critical_assets_affected'])}")
    print(f"Financial Impact: ${blast_result['financial_impact']:,.0f}")
    print(f"Recovery Time: {blast_result['recovery_time_estimate']} hours")

Attack Scenario Creation
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

    # Create attack scenario
    response = requests.post(
        f"{api_base}/attack-paths/scenarios",
        headers=headers,
        json={
            "scenario_name": "APT Campaign Simulation",
            "threat_actor": "APT29",
            "entry_points": ["internet_gateway", "email_server"],
            "objectives": ["database_001", "backup_server_001"]
        }
    )
    
    scenario = response.json()
    
    print(f"Scenario: {scenario['name']}")
    print(f"Risk Score: {scenario['total_risk_score']:.1f}")
    print(f"Criticality: {scenario['criticality_level']}")
    print(f"Attack Paths: {len(scenario['attack_paths'])}")
    print(f"Mitigation Strategies: {len(scenario['mitigation_strategies'])}")

Use Cases and Workflows
-----------------------

Security Assessment
~~~~~~~~~~~~~~~~~~~

**Objective**: Evaluate security posture and identify critical vulnerabilities.

**Workflow**:

1. **Baseline Analysis**
   
   * Run attack path analysis from all external entry points
   * Identify shortest paths to critical assets
   * Document current risk levels

2. **Vulnerability Impact Assessment**
   
   * Model attack paths from newly discovered vulnerabilities
   * Calculate blast radius from compromised systems
   * Prioritize remediation based on attack path analysis

3. **Architecture Review**
   
   * Analyze attack paths in proposed architecture changes
   * Validate security control effectiveness
   * Identify potential security gaps

Incident Response
~~~~~~~~~~~~~~~~~

**Objective**: Assess scope and impact during security incidents.

**Workflow**:

1. **Initial Assessment**
   
   * Calculate blast radius from confirmed compromised assets
   * Identify potentially affected systems
   * Estimate business impact

2. **Containment Planning**
   
   * Analyze attack paths to determine isolation points
   * Prioritize containment actions based on blast radius
   * Plan recovery sequence

3. **Forensic Analysis**
   
   * Model likely attack paths used by threat actor
   * Identify systems requiring forensic examination
   * Document attack progression for lessons learned

Red Team Exercises
~~~~~~~~~~~~~~~~~~

**Objective**: Plan and execute realistic attack simulations.

**Workflow**:

1. **Attack Planning**
   
   * Create attack scenarios based on threat intelligence
   * Identify optimal attack paths for objectives
   * Plan attack sequence and timing

2. **Exercise Execution**
   
   * Follow discovered attack paths during exercises
   * Test detection and response capabilities
   * Document successful attack vectors

3. **Post-Exercise Analysis**
   
   * Compare actual attack paths with predicted paths
   * Analyze detection gaps and response effectiveness
   * Generate recommendations for improvement

Purple Team Collaboration
~~~~~~~~~~~~~~~~~~~~~~~~~~

**Objective**: Collaborative security testing and validation.

**Workflow**:

1. **Scenario Development**
   
   * Red team creates attack scenarios using the tool
   * Blue team reviews scenarios and prepares defenses
   * Agree on exercise parameters and success criteria

2. **Collaborative Testing**
   
   * Execute attacks following predicted paths
   * Blue team monitors and responds in real-time
   * Document detection and response effectiveness

3. **Joint Analysis**
   
   * Review attack path predictions vs. actual results
   * Identify gaps in detection and response
   * Develop joint improvement recommendations

Best Practices
--------------

Analysis Configuration
~~~~~~~~~~~~~~~~~~~~~~

1. **Start Conservative**
   
   * Begin with shorter path lengths (3-4 hops)
   * Limit number of paths initially
   * Gradually increase scope as needed

2. **Focus on High-Value Targets**
   
   * Prioritize critical business assets
   * Include data stores with sensitive information
   * Consider compliance-critical systems

3. **Regular Updates**
   
   * Refresh analysis after infrastructure changes
   * Update after new vulnerabilities are discovered
   * Rerun analysis after security control changes

Interpretation Guidelines
~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Risk Prioritization**
   
   * Focus on CRITICAL and HIGH criticality paths first
   * Consider business context in addition to technical risk
   * Balance likelihood and impact in decision making

2. **Mitigation Planning**
   
   * Address shortest paths to critical assets first
   * Implement controls that affect multiple attack paths
   * Consider cost-effectiveness of mitigations

3. **Continuous Monitoring**
   
   * Set up alerts for new high-risk attack paths
   * Monitor changes in blast radius over time
   * Track mitigation effectiveness

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

1. **Graph Management**
   
   * Keep asset and relationship data current
   * Remove obsolete assets and relationships
   * Optimize graph structure for analysis performance

2. **Analysis Tuning**
   
   * Use appropriate path length limits
   * Leverage caching for repeated analysis
   * Clear cache periodically to free memory

3. **Resource Planning**
   
   * Plan for increased resource usage during large analysis
   * Consider running intensive analysis during off-peak hours
   * Monitor system performance during analysis

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**No Attack Paths Found**

* Verify source and target assets exist in the graph
* Check that relationships exist between assets
* Increase max_path_length parameter
* Verify assets are not isolated

**Performance Issues**

* Reduce max_path_length for large graphs
* Limit max_paths_per_target parameter
* Clear analysis cache if memory usage is high
* Consider running analysis during off-peak hours

**Unexpected Results**

* Verify asset metadata is current and accurate
* Check relationship definitions and weights
* Review security control configurations
* Validate business criticality assignments

Getting Help
~~~~~~~~~~~~

* **Documentation**: Refer to API documentation for detailed parameter descriptions
* **Support**: Contact support team for complex analysis issues
* **Community**: Join user community for best practices and tips
* **Training**: Attend training sessions for advanced features

Conclusion
----------

The attack path analysis capabilities provide powerful tools for:

* **Proactive Security**: Identify and address attack paths before they're exploited
* **Risk Assessment**: Quantify and prioritize security risks
* **Incident Response**: Rapidly assess scope and impact during incidents
* **Security Testing**: Plan and execute realistic attack simulations

By following the guidelines and best practices in this guide, you can effectively leverage these capabilities to strengthen your organization's security posture.
